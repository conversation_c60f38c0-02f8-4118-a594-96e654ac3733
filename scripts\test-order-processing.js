/**
 * Test script to verify order processing fixes
 * Run this in the browser console to test the order processing
 */

console.log('🧪 Testing Order Processing Fixes...\n');

// Test 1: Check if secure order processing service is available
console.log('1. Testing Secure Order Processing Service...');
try {
  // This would normally be imported, but for testing we'll check if it exists
  if (window.processOrderSecurely) {
    console.log('✅ Secure order processing service is available');
  } else {
    console.log('ℹ️ Secure order processing service not exposed to window (expected)');
  }
} catch (error) {
  console.log('❌ Error checking secure order processing:', error.message);
}

// Test 2: Test snack box cart item structure
console.log('\n2. Testing Snack Box Cart Item Structure...');
const mockSnackBoxItem = {
  id: 'vibe-1',
  name: 'Study Session Fuel',
  price: 299,
  original_price: 349,
  image_url: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400',
  image: 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=400',
  category: 'Snack Box',
  category_id: 'snack-box',
  description: 'Perfect snacks for those late-night study marathons',
  stock_quantity: 100,
  quantity: 1
};

// Validate required fields
const requiredFields = ['id', 'name', 'price', 'category', 'stock_quantity'];
const missingFields = requiredFields.filter(field => !mockSnackBoxItem[field]);

if (missingFields.length === 0) {
  console.log('✅ Snack box item has all required fields');
} else {
  console.log('❌ Missing required fields:', missingFields);
}

// Test 3: Test cart store if available
console.log('\n3. Testing Cart Store Integration...');
if (window.useCartStore) {
  try {
    const cartStore = window.useCartStore.getState();
    
    // Test adding snack box item
    console.log('Testing snack box item addition...');
    cartStore.addItem(mockSnackBoxItem);
    
    const items = cartStore.items;
    const addedItem = items.find(item => item.id === 'vibe-1');
    
    if (addedItem) {
      console.log('✅ Snack box item added successfully');
      console.log('Item details:', {
        id: addedItem.id,
        name: addedItem.name,
        category: addedItem.category,
        price: addedItem.price
      });
    } else {
      console.log('❌ Snack box item was not added to cart');
    }
    
  } catch (error) {
    console.log('❌ Cart store test failed:', error.message);
  }
} else {
  console.log('ℹ️ Cart store not available for testing');
}

// Test 4: Check for order processing errors in console
console.log('\n4. Monitoring for Order Processing Errors...');
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;
let orderErrors = [];

console.warn = function(...args) {
  const message = args.join(' ');
  if (message.includes('Order processing failed') || message.includes('order')) {
    orderErrors.push({ type: 'warn', message });
  }
  originalConsoleWarn.apply(console, args);
};

console.error = function(...args) {
  const message = args.join(' ');
  if (message.includes('Order processing failed') || message.includes('order')) {
    orderErrors.push({ type: 'error', message });
  }
  originalConsoleError.apply(console, args);
};

// Report order errors after delay
setTimeout(() => {
  console.warn = originalConsoleWarn;
  console.error = originalConsoleError;
  
  if (orderErrors.length === 0) {
    console.log('✅ No order processing errors detected');
  } else {
    console.log('⚠️ Order processing errors found:', orderErrors);
  }
}, 3000);

// Test 5: Validate price calculation logic
console.log('\n5. Testing Price Calculation Logic...');
const testCartItems = [mockSnackBoxItem];
const subtotal = testCartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
const deliveryFee = subtotal >= 149 ? 0 : (subtotal > 0 ? 10 : 0);
const convenienceFee = subtotal > 0 ? 6 : 0;
const total = subtotal + deliveryFee + convenienceFee;

console.log('Price calculation test:', {
  subtotal,
  deliveryFee,
  convenienceFee,
  total,
  expected: 299 + 10 + 6 // 315 for a 299 item
});

if (total === 315) {
  console.log('✅ Price calculation is correct');
} else {
  console.log('❌ Price calculation is incorrect');
}

console.log('\n🎯 Test Summary:');
console.log('- Order processing updated with fallback logic');
console.log('- Snack box items properly structured for cart');
console.log('- Price validation uses client-side calculation');
console.log('- Database RPC function calls have fallbacks');
console.log('\n💡 If you still see "Order processing failed" warnings:');
console.log('1. Check browser network tab for failed requests');
console.log('2. Verify user authentication status');
console.log('3. Check if required database tables exist');
console.log('4. Review console for specific error messages');
